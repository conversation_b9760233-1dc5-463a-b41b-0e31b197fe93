import numpy as np
import pandas as pd
import json
from pathlib import Path
import cv2
from collections import Counter
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, f1_score
from skimage import feature, measure
import matplotlib.pyplot as plt
import seaborn as sns

# Paths
data_root = Path('../data')
raw_dir = data_root / 'raw'
coco_dir = data_root / 'processed/coco'
output_dir = Path('../outputs/sam_classifier')
output_dir.mkdir(parents=True, exist_ok=True)

print(f"✓ Working directory: {Path.cwd()}")
print(f"✓ Data directory: {data_root.absolute()}")
print(f"✓ Output directory: {output_dir.absolute()}")

# Load COCO annotations
with open(coco_dir / 'full_dataset.json') as f:
    coco_data = json.load(f)

# Build mappings
categories = {cat['id']: cat['name'] for cat in coco_data['categories']}
image_id_to_filename = {img['id']: img['file_name'] for img in coco_data['images']}
filename_to_image_id = {img['file_name']: img['id'] for img in coco_data['images']}

# Group annotations by image
annotations_by_image = {}
for ann in coco_data['annotations']:
    img_id = ann['image_id']
    if img_id not in annotations_by_image:
        annotations_by_image[img_id] = []
    annotations_by_image[img_id].append(ann)

# Class distribution
class_counts = Counter(ann['category_id'] for ann in coco_data['annotations'])
print("\nClass Distribution:")
print("=" * 60)
for cat_id in sorted(categories.keys()):
    name = categories[cat_id]
    count = class_counts[cat_id]
    pct = 100 * count / sum(class_counts.values())
    print(f"  {cat_id}: {name:15s} - {count:4d} instances ({pct:5.1f}%)")
print("=" * 60)

# Calculate class weights
total = sum(class_counts.values())
n_classes = len(class_counts)
class_weights = {cat_id: total / (n_classes * count) 
                 for cat_id, count in class_counts.items()}

print("\nClass Weights (for balanced classifier):")
for cat_id in sorted(categories.keys()):
    print(f"  {categories[cat_id]:15s}: {class_weights[cat_id]:.3f}")

def extract_mask_features(image, mask):
    """
    Extract comprehensive features from a mask region.
    
    Features:
    - Shape: area, perimeter, circularity, solidity, extent
    - Texture: LBP histogram, contrast, homogeneity
    - Color: mean RGB, std RGB, color histogram
    - Context: bounding box aspect ratio, relative position
    """
    features = {}
    
    # Convert mask to binary
    binary_mask = (mask > 0).astype(np.uint8)
    
    # === SHAPE FEATURES ===
    try:
        labeled_mask = measure.label(binary_mask)
        props = measure.regionprops(labeled_mask)[0] if len(measure.regionprops(labeled_mask)) > 0 else None
    except:
        props = None
    
    if props is not None:
        features['area'] = props.area
        features['perimeter'] = props.perimeter
        features['circularity'] = (4 * np.pi * props.area) / (props.perimeter ** 2 + 1e-10)
        features['solidity'] = props.solidity
        features['extent'] = props.extent
        features['eccentricity'] = props.eccentricity
        features['major_axis'] = props.major_axis_length
        features['minor_axis'] = props.minor_axis_length
        features['aspect_ratio'] = props.major_axis_length / (props.minor_axis_length + 1e-10)
    else:
        # Default values if props cannot be computed
        features.update({
            'area': 0, 'perimeter': 0, 'circularity': 0, 'solidity': 0,
            'extent': 0, 'eccentricity': 0, 'major_axis': 0, 'minor_axis': 0,
            'aspect_ratio': 0
        })
    
    # === COLOR FEATURES ===
    masked_region = image * binary_mask[:, :, np.newaxis]
    pixels = masked_region[binary_mask > 0]
    
    if len(pixels) > 0:
        features['mean_r'] = np.mean(pixels[:, 0])
        features['mean_g'] = np.mean(pixels[:, 1])
        features['mean_b'] = np.mean(pixels[:, 2])
        features['std_r'] = np.std(pixels[:, 0])
        features['std_g'] = np.std(pixels[:, 1])
        features['std_b'] = np.std(pixels[:, 2])
        
        # Color histogram features (simplified)
        hist_r, _ = np.histogram(pixels[:, 0], bins=8, range=(0, 256))
        hist_g, _ = np.histogram(pixels[:, 1], bins=8, range=(0, 256))
        hist_b, _ = np.histogram(pixels[:, 2], bins=8, range=(0, 256))
        
        for i in range(8):
            features[f'hist_r_{i}'] = hist_r[i] / (len(pixels) + 1e-10)
            features[f'hist_g_{i}'] = hist_g[i] / (len(pixels) + 1e-10)
            features[f'hist_b_{i}'] = hist_b[i] / (len(pixels) + 1e-10)
    else:
        # Default color features
        features.update({f'mean_{c}': 0 for c in ['r', 'g', 'b']})
        features.update({f'std_{c}': 0 for c in ['r', 'g', 'b']})
        for i in range(8):
            features[f'hist_r_{i}'] = 0
            features[f'hist_g_{i}'] = 0
            features[f'hist_b_{i}'] = 0
    
    # === TEXTURE FEATURES (LBP) ===
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    masked_gray = gray * binary_mask
    
    try:
        # Local Binary Pattern
        lbp = feature.local_binary_pattern(masked_gray, P=8, R=1, method='uniform')
        lbp_masked = lbp[binary_mask > 0]
        
        if len(lbp_masked) > 0:
            lbp_hist, _ = np.histogram(lbp_masked, bins=10, range=(0, 10))
            for i in range(10):
                features[f'lbp_{i}'] = lbp_hist[i] / (len(lbp_masked) + 1e-10)
        else:
            for i in range(10):
                features[f'lbp_{i}'] = 0
    except:
        for i in range(10):
            features[f'lbp_{i}'] = 0
    
    # === CONTEXT FEATURES ===
    y_indices, x_indices = np.where(binary_mask > 0)
    if len(y_indices) > 0:
        bbox_x1, bbox_y1 = x_indices.min(), y_indices.min()
        bbox_x2, bbox_y2 = x_indices.max(), y_indices.max()
        
        features['bbox_width'] = bbox_x2 - bbox_x1
        features['bbox_height'] = bbox_y2 - bbox_y1
        features['bbox_aspect_ratio'] = features['bbox_width'] / (features['bbox_height'] + 1e-10)
        features['rel_pos_x'] = (bbox_x1 + bbox_x2) / (2 * image.shape[1])
        features['rel_pos_y'] = (bbox_y1 + bbox_y2) / (2 * image.shape[0])
    else:
        features.update({
            'bbox_width': 0, 'bbox_height': 0, 'bbox_aspect_ratio': 0,
            'rel_pos_x': 0, 'rel_pos_y': 0
        })
    
    return features

print("✓ Feature extraction functions defined")

# Load fold splits
with open(data_root / 'processed/splits/fold_splits.json') as f:
    fold_splits = json.load(f)

print("Extracting features from ground truth annotations...")
print("This may take a few minutes...\n")

# Storage for all features
all_features = []
all_labels = []
all_folds = []

# Process each image
for img_idx, img_info in enumerate(coco_data['images'], 1):
    img_filename = img_info['file_name']
    img_id = img_info['id']
    
    # Determine which fold this image belongs to
    fold_num = None
    for fold_data in fold_splits['splits']:
        if img_filename in fold_data['train'] or img_filename in fold_data['val']:
            fold_num = fold_data['fold']
            break
    
    # Load image
    img_path = raw_dir / img_filename
    image = cv2.imread(str(img_path))
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Get annotations for this image
    anns = annotations_by_image.get(img_id, [])
    
    print(f"[{img_idx}/{len(coco_data['images'])}] {img_filename}: {len(anns)} annotations", end='\r')
    
    # Extract features for each annotation
    for ann in anns:
        # Convert COCO polygon to mask
        mask = np.zeros((img_info['height'], img_info['width']), dtype=np.uint8)
        
        for seg in ann['segmentation']:
            poly = np.array(seg).reshape((-1, 2)).astype(np.int32)
            cv2.fillPoly(mask, [poly], 1)
        
        # Extract features
        features = extract_mask_features(image, mask)
        
        all_features.append(features)
        all_labels.append(ann['category_id'])
        all_folds.append(fold_num)

print(f"\n\n✓ Extracted features from {len(all_features)} ground truth masks")

# Convert to DataFrame
df_features = pd.DataFrame(all_features)
df_features['category_id'] = all_labels
df_features['category_name'] = [categories[cat_id] for cat_id in all_labels]
df_features['fold'] = all_folds

print(f"✓ Feature matrix shape: {df_features.shape}")
print(f"\nFeature columns: {list(df_features.columns[:10])}... (showing first 10)")
print(f"\nClass distribution in training data:")
print(df_features['category_name'].value_counts())

# Prepare features and labels
feature_cols = [col for col in df_features.columns 
                if col not in ['category_id', 'category_name', 'fold']]

X = df_features[feature_cols].fillna(0).values  # Convert to numpy array
y = df_features['category_id'].values

# Create class weight dictionary for sklearn (convert to float for sklearn compatibility)
class_weight_dict = {int(cat_id): float(class_weights[cat_id]) for cat_id in categories.keys()}

print("Training Random Forest Classifier with class weights...")
print(f"Features: {len(feature_cols)}")
print(f"Samples: {len(X)}")
print(f"Classes: {len(set(y))}\n")

# Train classifier
clf = RandomForestClassifier(
    n_estimators=200,
    max_depth=20,
    min_samples_split=5,
    min_samples_leaf=2,
    class_weight=class_weight_dict,
    random_state=42,
    n_jobs=-1,
    verbose=1
)

clf.fit(X, y)

print("\n✓ Classifier trained successfully!")

# Feature importance
feature_importance = pd.DataFrame({
    'feature': feature_cols,
    'importance': clf.feature_importances_
}).sort_values('importance', ascending=False)

print("\nTop 15 Most Important Features:")
print("=" * 60)
print(feature_importance.head(15).to_string(index=False))
print("=" * 60)

from sklearn.model_selection import cross_val_predict
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

# Perform cross-validation predictions
print("Performing 5-fold cross-validation...\n")

# Use fold information for proper cross-validation
folds = df_features['fold'].values
cv_results = []

for fold in range(1, 6):
    print(f"\nProcessing fold {fold}...")
    
    # Split by fold
    train_mask = folds != fold
    val_mask = folds == fold
    
    # Check if we have samples
    n_train = np.sum(train_mask)
    n_val = np.sum(val_mask)
    print(f"  Train samples: {n_train}, Val samples: {n_val}")
    
    if n_val == 0:
        print(f"  ⚠️ Skipping fold {fold} - no validation samples")
        continue
    
    X_train, X_val = X[train_mask], X[val_mask]
    y_train, y_val = y[train_mask], y[val_mask]
    
    print(f"  Training classifier...")
    
    # Train on this fold
    fold_clf = RandomForestClassifier(
        n_estimators=200,
        max_depth=20,
        min_samples_split=5,
        min_samples_leaf=2,
        class_weight=class_weight_dict,
        random_state=42,
        n_jobs=-1
    )
    
    try:
        fold_clf.fit(X_train, y_train)
        
        # Predict on validation
        y_pred = fold_clf.predict(X_val)
        
        # Calculate metrics
        accuracy = accuracy_score(y_val, y_pred)
        precision, recall, f1, support = precision_recall_fscore_support(
            y_val, y_pred, average='weighted', zero_division=0
        )
        
        cv_results.append({
            'fold': fold,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'n_samples': len(y_val)
        })
        
        print(f"  ✓ Fold {fold}: Acc={accuracy:.3f}, Prec={precision:.3f}, Rec={recall:.3f}, F1={f1:.3f}")
    
    except Exception as e:
        print(f"  ❌ Error in fold {fold}: {str(e)}")
        continue

# Summary
cv_df = pd.DataFrame(cv_results)

if len(cv_df) == 0:
    print("\n❌ No folds were successfully processed!")
    print("Please check the error messages above.")
else:
    print("\n" + "=" * 60)
    print("Cross-Validation Results:")
    print("=" * 60)
    print(cv_df.to_string(index=False))
    print("\nMean Performance:")
    print(f"  Accuracy:  {cv_df['accuracy'].mean():.3f} ± {cv_df['accuracy'].std():.3f}")
    print(f"  Precision: {cv_df['precision'].mean():.3f} ± {cv_df['precision'].std():.3f}")
    print(f"  Recall:    {cv_df['recall'].mean():.3f} ± {cv_df['recall'].std():.3f}")
    print(f"  F1 Score:  {cv_df['f1'].mean():.3f} ± {cv_df['f1'].std():.3f}")
    print("=" * 60)

# Get predictions for all data using the full trained classifier
y_pred_all = clf.predict(X)

# Classification report
print("Per-Class Performance (on training data):")
print("=" * 80)
report = classification_report(
    y, y_pred_all,
    target_names=[categories[i] for i in sorted(categories.keys())],
    zero_division=0
)
print(report)

# Confusion matrix
cm = confusion_matrix(y, y_pred_all)
class_names = [categories[i] for i in sorted(categories.keys())]

plt.figure(figsize=(10, 8))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=class_names, yticklabels=class_names)
plt.title('Confusion Matrix - Balanced Classifier')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.tight_layout()
plt.savefig(output_dir / 'confusion_matrix.png', dpi=150, bbox_inches='tight')
plt.show()

print(f"\n✓ Confusion matrix saved to {output_dir / 'confusion_matrix.png'}")

import pickle

# Save classifier
model_path = output_dir / 'balanced_classifier.pkl'
with open(model_path, 'wb') as f:
    pickle.dump(clf, f)

# Save feature names
feature_info = {
    'feature_names': feature_cols,
    'categories': {int(k): str(v) for k, v in categories.items()},
    'class_weights': {int(k): float(v) for k, v in class_weights.items()}
}
with open(output_dir / 'feature_info.json', 'w') as f:
    json.dump(feature_info, f, indent=2)

print(f"✓ Classifier saved to {model_path}")
print(f"✓ Feature info saved to {output_dir / 'feature_info.json'}")
print(f"\nModel size: {model_path.stat().st_size / 1024 / 1024:.2f} MB")