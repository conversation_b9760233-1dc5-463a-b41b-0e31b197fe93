{"cells": [{"cell_type": "markdown", "id": "6769ee1d", "metadata": {}, "source": ["# Mask R-CNN Instance Segmentation - Google Colab\n", "\n", "**Setup:** Runtime → Change runtime type → GPU (T4)\n", "\n", "Training: Mask R-CNN (ResNet-50-FPN), 3000 iterations, 5-fold CV"]}, {"cell_type": "markdown", "id": "f154a02f", "metadata": {}, "source": ["## 1. Mount Google Drive & Setup"]}, {"cell_type": "code", "execution_count": null, "id": "12fe3c2c", "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "import os\n", "os.chdir('/content/drive/MyDrive/FaciesNet')\n", "print(f\"Working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "id": "942cc7a6", "metadata": {}, "source": ["## 2. Install Detectron2 & Check GPU"]}, {"cell_type": "code", "execution_count": null, "id": "4acd7758", "metadata": {}, "outputs": [], "source": ["!pip install -q 'git+https://github.com/facebookresearch/detectron2.git'\n", "\n", "import torch\n", "print(f\"GPU: {torch.cuda.get_device_name(0)}, VRAM: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB\")"]}, {"cell_type": "markdown", "id": "9bcb20eb", "metadata": {}, "source": ["## 3. Configuration"]}, {"cell_type": "code", "execution_count": null, "id": "459ac5bd", "metadata": {}, "outputs": [], "source": ["from detectron2.engine import DefaultTrainer\n", "from detectron2.config import get_cfg\n", "from detectron2 import model_zoo\n", "from detectron2.data import DatasetCatalog, MetadataCatalog\n", "from detectron2.data.datasets import register_coco_instances\n", "from detectron2.evaluation import COCOEvaluator, inference_on_dataset\n", "from detectron2.data import build_detection_test_loader\n", "from pathlib import Path\n", "import json, pandas as pd\n", "\n", "data_dir = Path('data/processed/coco')\n", "images_dir = Path('data/raw')\n", "results_dir = Path('outputs/maskrcnn_experiments')\n", "results_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Load class names from COCO file (after exclusion in data prep)\n", "with open(data_dir / 'full_dataset.json') as f:\n", "    class_names = [cat['name'] for cat in json.load(f)['categories']]"]}, {"cell_type": "markdown", "id": "2bed1816", "metadata": {}, "source": ["## 4. Fix & Register Datasets"]}, {"cell_type": "code", "execution_count": null, "id": "3f5277dc", "metadata": {}, "outputs": [], "source": ["# Fix COCO files - add missing \"info\" field\n", "for fold in range(1, 6):\n", "    for split in ['train', 'val']:\n", "        json_file = data_dir / f'fold{fold}_{split}.json'\n", "        with open(json_file, 'r') as f:\n", "            data = json.load(f)\n", "        \n", "        # Add missing \"info\" field if not present\n", "        if 'info' not in data:\n", "            data['info'] = {\n", "                \"description\": \"FaciesNet Carbonate Dataset\",\n", "                \"version\": \"1.0\",\n", "                \"year\": 2025,\n", "                \"contributor\": \"FaciesNet\",\n", "                \"date_created\": \"2025/10/15\"\n", "            }\n", "            with open(json_file, 'w') as f:\n", "                json.dump(data, f)\n", "\n", "# Now register datasets\n", "for fold in range(1, 6):\n", "    for split in ['train', 'val']:\n", "        name = f'facies_fold{fold}_{split}'\n", "        json_file = str(data_dir / f'fold{fold}_{split}.json')\n", "        img_dir = str(images_dir)\n", "        \n", "        if name in DatasetCatalog.list():\n", "            DatasetCatalog.remove(name)\n", "            MetadataCatalog.remove(name)\n", "        \n", "        register_coco_instances(name, {}, json_file, img_dir)\n", "        MetadataCatalog.get(name).thing_classes = class_names\n", "\n", "print(f\"✓ Fixed and registered {len(DatasetCatalog.list())} datasets\")"]}, {"cell_type": "markdown", "id": "4685e54b", "metadata": {}, "source": ["## 5. Train 5 Folds (~4 hours)"]}, {"cell_type": "code", "execution_count": null, "id": "ddfd2c1d", "metadata": {}, "outputs": [], "source": ["all_results = {}\n", "\n", "for fold in range(1, 6):\n", "    print(f\"\\n{'='*60}\\nFold {fold}/5\\n{'='*60}\")\n", "    \n", "    cfg = get_cfg()\n", "    cfg.merge_from_file(model_zoo.get_config_file(\"COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml\"))\n", "    cfg.DATASETS.TRAIN = (f\"facies_fold{fold}_train\",)\n", "    cfg.DATASETS.TEST = (f\"facies_fold{fold}_val\",)\n", "    cfg.DATALOADER.NUM_WORKERS = 2\n", "    cfg.MODEL.WEIGHTS = model_zoo.get_checkpoint_url(\"COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml\")\n", "    cfg.SOLVER.IMS_PER_BATCH = 2\n", "    cfg.SOLVER.BASE_LR = 0.001\n", "    cfg.SOLVER.MAX_ITER = 3000\n", "    cfg.SOLVER.STEPS = (2000, 2500)\n", "    cfg.MODEL.ROI_HEADS.BATCH_SIZE_PER_IMAGE = 512\n", "    cfg.MODEL.ROI_HEADS.NUM_CLASSES = len(class_names)\n", "    cfg.OUTPUT_DIR = str(results_dir / f'fold{fold}')\n", "    Path(cfg.OUTPUT_DIR).mkdir(parents=True, exist_ok=True)\n", "    \n", "    trainer = De<PERSON><PERSON><PERSON><PERSON><PERSON>(cfg)\n", "    trainer.resume_or_load(resume=False)\n", "    trainer.train()\n", "    \n", "    evaluator = COCOEvaluator(f\"facies_fold{fold}_val\", output_dir=cfg.OUTPUT_DIR)\n", "    val_loader = build_detection_test_loader(cfg, f\"facies_fold{fold}_val\")\n", "    results = inference_on_dataset(trainer.model, val_loader, evaluator)\n", "    \n", "    all_results[f'fold{fold}'] = {\n", "        'AP': results['segm']['AP'],\n", "        'AP50': results['segm']['AP50'],\n", "        'AP75': results['segm']['AP75'],\n", "    }\n", "    \n", "    print(f\"Fold {fold}: AP={all_results[f'fold{fold}']['AP']:.2f}, AP50={all_results[f'fold{fold}']['AP50']:.2f}\")\n", "\n", "print(\"\\n\" + \"=\"*60 + \"\\nTraining Complete\\n\" + \"=\"*60)"]}, {"cell_type": "markdown", "id": "5b93db5e", "metadata": {}, "source": ["## 6. Results Summary"]}, {"cell_type": "code", "execution_count": null, "id": "5bf3d996", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "df = pd.DataFrame(all_results).T\n", "df['Fold'] = df.index\n", "df = df[['Fold', 'AP', 'AP50', 'AP75']]\n", "\n", "mean_row = pd.DataFrame([{\n", "    'Fold': 'Mean',\n", "    'AP': df['AP'].mean(),\n", "    'AP50': df['AP50'].mean(),\n", "    'AP75': df['AP75'].mean()\n", "}])\n", "std_row = pd.DataFrame([{\n", "    'Fold': 'Std',\n", "    'AP': df['AP'].std(),\n", "    'AP50': df['AP50'].std(),\n", "    'AP75': df['AP75'].std()\n", "}])\n", "\n", "df = pd.concat([df, mean_row, std_row], ignore_index=True)\n", "print(df.to_string(index=False))\n", "\n", "df.to_csv(results_dir / 'cross_validation_results.csv', index=False)\n", "print(f\"\\nSaved: {results_dir / 'cross_validation_results.csv'}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}