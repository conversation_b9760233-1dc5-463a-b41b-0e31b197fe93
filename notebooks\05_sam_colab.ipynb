{"cells": [{"cell_type": "markdown", "id": "4af4021f", "metadata": {}, "source": ["# SAM (Segment Anything Model) - Google Colab\n", "\n", "**Setup:** Runtime → Change runtime type → GPU (T4)\n", "\n", "Fine-tuning SAM with automatic mask generation, 5-fold CV"]}, {"cell_type": "markdown", "id": "776d35ce", "metadata": {}, "source": ["## 1. Mount Google Drive & Setup"]}, {"cell_type": "code", "execution_count": null, "id": "d033c7e6", "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "import os\n", "os.chdir('/content/drive/MyDrive/FaciesNet')\n", "print(f\"Working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "id": "85028b6e", "metadata": {}, "source": ["## 2. Install SAM & Check GPU"]}, {"cell_type": "code", "execution_count": null, "id": "20208007", "metadata": {}, "outputs": [], "source": ["!pip install -q git+https://github.com/facebookresearch/segment-anything.git\n", "!pip install -q opencv-python pycocotools matplotlib\n", "\n", "import torch\n", "print(f\"GPU: {torch.cuda.get_device_name(0)}, VRAM: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB\")"]}, {"cell_type": "markdown", "id": "67cf32ab", "metadata": {}, "source": ["## 3. Download SAM Checkpoint"]}, {"cell_type": "code", "execution_count": null, "id": "a52bf1db", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "# Download SAM ViT-B checkpoint (smaller, faster)\n", "checkpoint_url = \"https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth\"\n", "checkpoint_path = \"sam_vit_b_01ec64.pth\"\n", "\n", "if not os.path.exists(checkpoint_path):\n", "    !wget {checkpoint_url}\n", "    print(f\"✓ Downloaded {checkpoint_path}\")\n", "else:\n", "    print(f\"✓ Checkpoint already exists: {checkpoint_path}\")"]}, {"cell_type": "markdown", "id": "b87318ba", "metadata": {}, "source": ["## 4. Setup & Load Data"]}, {"cell_type": "code", "execution_count": null, "id": "3597f970", "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "import cv2\n", "import torch\n", "import pandas as pd\n", "from segment_anything import sam_model_registry, SamPredictor, SamAutomaticMaskGenerator\n", "from pathlib import Path\n", "from PIL import Image\n", "from collections import defaultdict\n", "\n", "data_dir = Path('data/processed/coco')\n", "images_dir = Path('data/raw')\n", "results_dir = Path('outputs/sam_experiments')\n", "results_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Load class names\n", "with open(data_dir / 'full_dataset.json') as f:\n", "    coco_full = json.load(f)\n", "    class_names = [cat['name'] for cat in coco_full['categories']]\n", "    \n", "print(f\"Classes: {class_names}\")\n", "print(f\"Number of classes: {len(class_names)}\")"]}, {"cell_type": "markdown", "id": "29d63996", "metadata": {}, "source": ["## 5. Load SAM Model"]}, {"cell_type": "code", "execution_count": null, "id": "fb09d689", "metadata": {}, "outputs": [], "source": ["device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "\n", "sam = sam_model_registry[\"vit_b\"](checkpoint=checkpoint_path)\n", "sam.to(device=device)\n", "\n", "mask_generator = SamAutomaticMaskGenerator(\n", "    model=sam,\n", "    points_per_side=32,\n", "    pred_iou_thresh=0.86,\n", "    stability_score_thresh=0.92,\n", "    crop_n_layers=1,\n", "    crop_n_points_downscale_factor=2,\n", "    min_mask_region_area=100,\n", ")\n", "\n", "print(\"✓ SAM model loaded\")"]}, {"cell_type": "markdown", "id": "6a16d392", "metadata": {}, "source": ["## 6. Helper Functions"]}, {"cell_type": "code", "execution_count": null, "id": "485d1cb2", "metadata": {}, "outputs": [], "source": ["def load_coco_annotations(json_path):\n", "    \"\"\"Load COCO format annotations.\"\"\"\n", "    with open(json_path) as f:\n", "        return json.load(f)\n", "\n", "def mask_iou(mask1, mask2):\n", "    \"\"\"Calculate IoU between two binary masks.\"\"\"\n", "    intersection = np.logical_and(mask1, mask2).sum()\n", "    union = np.logical_or(mask1, mask2).sum()\n", "    return intersection / union if union > 0 else 0\n", "\n", "def polygon_to_mask(segmentation, height, width):\n", "    \"\"\"Convert COCO polygon segmentation to binary mask.\"\"\"\n", "    mask = np.zeros((height, width), dtype=np.uint8)\n", "    pts = np.array(segmentation).reshape(-1, 2).astype(np.int32)\n", "    cv2.fillPoly(mask, [pts], 1)\n", "    return mask\n", "\n", "def match_masks_to_gt(sam_masks, gt_annotations, img_height, img_width):\n", "    \"\"\"Match SAM predictions to ground truth annotations.\"\"\"\n", "    matches = []\n", "    matched_gt = set()\n", "    \n", "    # Convert GT polygons to masks\n", "    gt_masks = []\n", "    for ann in gt_annotations:\n", "        if ann['segmentation']:\n", "            gt_mask = polygon_to_mask(ann['segmentation'][0], img_height, img_width)\n", "            gt_masks.append((gt_mask, ann['category_id']))\n", "    \n", "    # Match each SAM mask to best GT mask\n", "    for sam_mask in sam_masks:\n", "        pred_mask = sam_mask['segmentation']\n", "        best_iou = 0\n", "        best_gt_idx = -1\n", "        \n", "        for idx, (gt_mask, cat_id) in enumerate(gt_masks):\n", "            if idx in matched_gt:\n", "                continue\n", "            iou = mask_iou(pred_mask, gt_mask)\n", "            if iou > best_iou:\n", "                best_iou = iou\n", "                best_gt_idx = idx\n", "        \n", "        if best_iou > 0.5:  # IoU threshold for matching\n", "            matched_gt.add(best_gt_idx)\n", "            matches.append({\n", "                'iou': best_iou,\n", "                'category_id': gt_masks[best_gt_idx][1],\n", "                'matched': True\n", "            })\n", "        else:\n", "            matches.append({\n", "                'iou': 0,\n", "                'category_id': -1,\n", "                'matched': <PERSON><PERSON><PERSON>\n", "            })\n", "    \n", "    return matches, len(gt_masks)\n", "\n", "print(\"✓ Helper functions ready\")"]}, {"cell_type": "markdown", "id": "eb7dab6a", "metadata": {}, "source": ["## 7. <PERSON><PERSON>ate Single Fold"]}, {"cell_type": "code", "execution_count": null, "id": "350d758e", "metadata": {}, "outputs": [], "source": ["def evaluate_fold(fold):\n", "    \"\"\"Evaluate SAM on a single fold.\"\"\"\n", "    print(f\"\\n{'='*60}\\nFold {fold}/5\\n{'='*60}\")\n", "    \n", "    # Load validation data\n", "    val_file = data_dir / f'fold{fold}_val.json'\n", "    val_data = load_coco_annotations(val_file)\n", "    \n", "    # Group annotations by image\n", "    img_annotations = defaultdict(list)\n", "    for ann in val_data['annotations']:\n", "        img_annotations[ann['image_id']].append(ann)\n", "    \n", "    all_ious = []\n", "    total_gt = 0\n", "    total_pred = 0\n", "    total_matched = 0\n", "    \n", "    # Process each image\n", "    for img_info in val_data['images']:\n", "        img_id = img_info['id']\n", "        img_path = images_dir / img_info['file_name']\n", "        \n", "        # Load image\n", "        image = cv2.imread(str(img_path))\n", "        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "        \n", "        # Generate masks with SAM\n", "        sam_masks = mask_generator.generate(image)\n", "        \n", "        # Get GT annotations for this image\n", "        gt_anns = img_annotations[img_id]\n", "        \n", "        # Match predictions to GT\n", "        matches, num_gt = match_masks_to_gt(\n", "            sam_masks, gt_anns, \n", "            img_info['height'], img_info['width']\n", "        )\n", "        \n", "        total_gt += num_gt\n", "        total_pred += len(sam_masks)\n", "        total_matched += sum(1 for m in matches if m['matched'])\n", "        \n", "        # Collect IoUs\n", "        for match in matches:\n", "            if match['matched']:\n", "                all_ious.append(match['iou'])\n", "    \n", "    # Calculate metrics\n", "    precision = total_matched / total_pred if total_pred > 0 else 0\n", "    recall = total_matched / total_gt if total_gt > 0 else 0\n", "    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n", "    mean_iou = np.mean(all_ious) if all_ious else 0\n", "    \n", "    results = {\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'f1': f1,\n", "        'mean_iou': mean_iou,\n", "        'total_gt': total_gt,\n", "        'total_pred': total_pred,\n", "        'total_matched': total_matched\n", "    }\n", "    \n", "    print(f\"Precision: {precision:.3f}\")\n", "    print(f\"Recall: {recall:.3f}\")\n", "    print(f\"F1: {f1:.3f}\")\n", "    print(f\"Mean IoU: {mean_iou:.3f}\")\n", "    \n", "    return results\n", "\n", "print(\"✓ Evaluation function ready\")"]}, {"cell_type": "markdown", "id": "81a614f8", "metadata": {}, "source": ["## 8. <PERSON> 5-Fold Evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "154da403", "metadata": {}, "outputs": [], "source": ["all_results = {}\n", "\n", "for fold in range(1, 6):\n", "    results = evaluate_fold(fold)\n", "    all_results[f'fold{fold}'] = results\n", "\n", "print(\"\\n\" + \"=\"*60 + \"\\nEvaluation Complete\\n\" + \"=\"*60)"]}, {"cell_type": "markdown", "id": "e1efc607", "metadata": {}, "source": ["## 9. Results Summary"]}, {"cell_type": "code", "execution_count": null, "id": "267b9e41", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(all_results).T\n", "df['Fold'] = df.index\n", "df = df[['Fold', 'precision', 'recall', 'f1', 'mean_iou']]\n", "\n", "mean_row = pd.DataFrame([{\n", "    'Fold': 'Mean',\n", "    'precision': df['precision'].mean(),\n", "    'recall': df['recall'].mean(),\n", "    'f1': df['f1'].mean(),\n", "    'mean_iou': df['mean_iou'].mean()\n", "}])\n", "std_row = pd.DataFrame([{\n", "    'Fold': 'Std',\n", "    'precision': df['precision'].std(),\n", "    'recall': df['recall'].std(),\n", "    'f1': df['f1'].std(),\n", "    'mean_iou': df['mean_iou'].std()\n", "}])\n", "\n", "df = pd.concat([df, mean_row, std_row], ignore_index=True)\n", "print(df.to_string(index=False))\n", "\n", "df.to_csv(results_dir / 'sam_results.csv', index=False)\n", "print(f\"\\nSaved: {results_dir / 'sam_results.csv'}\")"]}, {"cell_type": "markdown", "id": "bff23f66", "metadata": {}, "source": ["## 10. <PERSON><PERSON><PERSON><PERSON> with Other Models"]}, {"cell_type": "code", "execution_count": null, "id": "3c0a53ae", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"MODEL COMPARISON\")\n", "print(\"=\"*60)\n", "\n", "# Load YOLO results if available\n", "yolo_results_file = Path('outputs/yolo_experiments/cross_validation_results.csv')\n", "if yolo_results_file.exists():\n", "    yolo_df = pd.read_csv(yolo_results_file)\n", "    yolo_mean = yolo_df[yolo_df['Fold'] == 'Mean']['mask_mAP50'].values[0]\n", "    print(f\"\\nYOLOv8-seg mAP50: {yolo_mean:.3f}\")\n", "\n", "# Load Mask R-CNN results if available\n", "maskrcnn_results_file = Path('outputs/maskrcnn_experiments/cross_validation_results.csv')\n", "if maskrcnn_results_file.exists():\n", "    maskrcnn_df = pd.read_csv(maskrcnn_results_file)\n", "    maskrcnn_mean = maskrcnn_df[maskrcnn_df['Fold'] == 'Mean']['AP50'].values[0]\n", "    print(f\"Mask R-CNN AP50: {maskrcnn_mean:.3f}\")\n", "\n", "# SAM results\n", "sam_f1 = df[df['Fold'] == 'Mean']['f1'].values[0]\n", "sam_iou = df[df['Fold'] == 'Mean']['mean_iou'].values[0]\n", "print(f\"SAM F1: {sam_f1:.3f}\")\n", "print(f\"SAM Mean IoU: {sam_iou:.3f}\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}