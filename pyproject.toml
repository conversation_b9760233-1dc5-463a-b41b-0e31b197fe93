[project]
name = "faciesnet"
version = "0.1.0"
description = "Deep learning for carbonate thin-section segmentation"
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "matplotlib>=3.4.0",
    "seaborn>=0.11.0",
    "Pillow>=8.3.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
    "scikit-image>=0.18.0",
    "opencv-python>=4.5.0",
    "tqdm>=4.62.0",
    # For SAM
    "segment-anything @ git+https://github.com/facebookresearch/segment-anything.git",
    # For YOLO
    "ultralytics>=8.0.0",
    # For evaluation
    "pycocotools>=2.0.0",
    # For interpretability
    "grad-cam>=1.4.0",
    "shap>=0.42.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "ruff>=0.0.250",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.ruff]
line-length = 100
target-version = "py38"

[tool.black]
line-length = 100
target-version = ["py38"]
