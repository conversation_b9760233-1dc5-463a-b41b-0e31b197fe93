# FaciesNet - Carbonate Thin-Section Segmentation

Deep learning model for automatic identification and segmentation of carbonate microfacies components in petrographic thin-section images.

## Project Structure

```
FaciesNet/
├── data/                          # Raw data (images + JSON annotations)
│   ├── *.jpg/JPG                  # Thin-section images
│   └── *.json                     # LabelMe format annotations
│
├── notebooks/                     # Jupyter notebooks for exploration & experiments
│   └── 01_data_exploration.ipynb  # Initial data analysis
│
├── src/                           # Source code (to be developed)
│
├── outputs/                       # Generated outputs
│   └── visualizations/            # Plots and visualizations
│
└── requirements.txt               # Python dependencies
```

## Dataset

- **Images**: 18 high-resolution RGB thin-section images
- **Classes**: 5 carbonate microfacies components (reduced from 7)
  - Peloid
  - Ooid
  - Intraclast
  - Ostracod
  - Broken ooid
  - ~~Quartz grain~~ (removed - 9 instances)
  - ~~Bivalve~~ (removed - 4 instances)
- **Format**: LabelMe JSON annotations with polygon masks
- **Total instances**: ~2,674 annotated instances (after removing rare classes)

**Note**: Bivalve and Quartz grain classes were removed due to insufficient instances (<10 each), which negatively impacted model performance.

## Getting Started

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Explore the Data

Open and run the data exploration notebook:

```bash
jupyter notebook notebooks/01_data_exploration.ipynb
```

This will:
- Load and validate all data files
- Analyze class distribution
- Visualize sample annotations
- Generate statistical summaries
- Save visualizations to `outputs/visualizations/`

## Development Status

- [x] Project setup
- [x] Data exploration
- [x] Data preparation (COCO & YOLO formats)
- [x] 5-fold cross-validation splits
- [x] Remove rare classes (Bivalve, Quartz grain)
- [ ] YOLOv8-seg implementation
- [ ] Mask R-CNN implementation
- [ ] SAM implementation
- [ ] Model comparison & evaluation
- [ ] Interpretability analysis

## Notes

- Keeping the codebase simple and professional
- Step-by-step development approach
- Focus on interpretability and scientific rigor
