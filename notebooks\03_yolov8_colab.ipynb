from google.colab import drive
drive.mount('/content/drive')

import os
os.chdir('/content/drive/MyDrive/FaciesNet')
print(f"Working directory: {os.getcwd()}")

!pip install -q ultralytics

import torch
print(f"GPU: {torch.cuda.get_device_name(0)}, VRAM: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB")

from ultralytics import YOLO
from pathlib import Path
import json, pandas as pd
from datetime import datetime

data_dir = Path('data/processed/yolo')
results_dir = Path('outputs/yolo_experiments')
results_dir.mkdir(parents=True, exist_ok=True)

config = {
    'model': 'yolov8s-seg.pt',
    'epochs': 50,
    'imgsz': 1280,
    'batch': 4,
    'device': 0,
    'project': str(results_dir),
    'patience': 20,
    'save': True,
    'save_period': -1,
    'workers': 4,
    'optimizer': 'AdamW',
    'lr0': 0.001,
    'momentum': 0.9,
    'weight_decay': 0.0005,
    'amp': True,
}

n_folds = 5

all_results = {}

for fold in range(1, n_folds + 1):
    print(f"\n{'='*60}\nFold {fold}/{n_folds}\n{'='*60}")
    
    model = YOLO(config['model'])
    results = model.train(
        data=str(data_dir / f'fold{fold}_config.yaml'),
        epochs=config['epochs'],
        imgsz=config['imgsz'],
        batch=config['batch'],
        device=config['device'],
        project=config['project'],
        name=f'fold{fold}',
        patience=config['patience'],
        save=config['save'],
        save_period=config['save_period'],
        workers=config['workers'],
        optimizer=config['optimizer'],
        lr0=config['lr0'],
        momentum=config['momentum'],
        weight_decay=config['weight_decay'],
        amp=config['amp'],
        exist_ok=True,
    )
    
    val_results = model.val()
    
    all_results[f'fold{fold}'] = {
        'mask_mAP50': float(val_results.seg.map50),
        'mask_mAP50_95': float(val_results.seg.map),
    }
    
    print(f"Fold {fold}: mAP50={all_results[f'fold{fold}']['mask_mAP50']:.3f}, mAP50-95={all_results[f'fold{fold}']['mask_mAP50_95']:.3f}")

print("\n" + "="*60 + "\nTraining Complete\n" + "="*60)

import numpy as np

df = pd.DataFrame(all_results).T
df['Fold'] = df.index
df = df[['Fold', 'mask_mAP50', 'mask_mAP50_95']]

mean_row = pd.DataFrame([{
    'Fold': 'Mean',
    'mask_mAP50': df['mask_mAP50'].mean(),
    'mask_mAP50_95': df['mask_mAP50_95'].mean()
}])
std_row = pd.DataFrame([{
    'Fold': 'Std',
    'mask_mAP50': df['mask_mAP50'].std(),
    'mask_mAP50_95': df['mask_mAP50_95'].std()
}])

df = pd.concat([df, mean_row, std_row], ignore_index=True)
print(df.to_string(index=False))

df.to_csv(results_dir / 'cross_validation_results.csv', index=False)
print(f"\nSaved: {results_dir / 'cross_validation_results.csv'}")