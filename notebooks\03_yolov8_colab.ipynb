{"cells": [{"cell_type": "markdown", "id": "1298fc04", "metadata": {}, "source": ["# YOLOv8-seg Instance Segmentation - Google Colab\n", "\n", "**Setup:** Runtime → Change runtime type → GPU (T4)\n", "\n", "Training: YOLOv8s-seg, 50 epochs, 1280px, batch=4, 5-fold CV"]}, {"cell_type": "markdown", "id": "74d6b5ac", "metadata": {}, "source": ["## 1. Mount Google Drive & Setup"]}, {"cell_type": "code", "execution_count": null, "id": "26573e81", "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "import os\n", "os.chdir('/content/drive/MyDrive/FaciesNet')\n", "print(f\"Working directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "id": "d81b4850", "metadata": {}, "source": ["## 2. Install & Check GPU"]}, {"cell_type": "code", "execution_count": null, "id": "60363529", "metadata": {}, "outputs": [], "source": ["!pip install -q ultralytics\n", "\n", "import torch\n", "print(f\"GPU: {torch.cuda.get_device_name(0)}, VRAM: {torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB\")"]}, {"cell_type": "markdown", "id": "7eeab83e", "metadata": {}, "source": ["## 3. Configuration"]}, {"cell_type": "code", "execution_count": null, "id": "506aaa0d", "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO\n", "from pathlib import Path\n", "import json, pandas as pd\n", "from datetime import datetime\n", "\n", "data_dir = Path('data/processed/yolo')\n", "results_dir = Path('outputs/yolo_experiments')\n", "results_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "config = {\n", "    'model': 'yolov8s-seg.pt',\n", "    'epochs': 50,\n", "    'imgsz': 1280,\n", "    'batch': 4,\n", "    'device': 0,\n", "    'project': str(results_dir),\n", "    'patience': 20,\n", "    'save': True,\n", "    'save_period': -1,\n", "    'workers': 4,\n", "    'optimizer': 'AdamW',\n", "    'lr0': 0.001,\n", "    'momentum': 0.9,\n", "    'weight_decay': 0.0005,\n", "    'amp': True,\n", "}\n", "\n", "n_folds = 5"]}, {"cell_type": "markdown", "id": "6203a28d", "metadata": {}, "source": ["## 4. <PERSON> 5 Folds (~3 hours)"]}, {"cell_type": "code", "execution_count": null, "id": "d2ff0205", "metadata": {}, "outputs": [], "source": ["all_results = {}\n", "\n", "for fold in range(1, n_folds + 1):\n", "    print(f\"\\n{'='*60}\\nFold {fold}/{n_folds}\\n{'='*60}\")\n", "    \n", "    model = YOLO(config['model'])\n", "    results = model.train(\n", "        data=str(data_dir / f'fold{fold}_config.yaml'),\n", "        epochs=config['epochs'],\n", "        imgsz=config['imgsz'],\n", "        batch=config['batch'],\n", "        device=config['device'],\n", "        project=config['project'],\n", "        name=f'fold{fold}',\n", "        patience=config['patience'],\n", "        save=config['save'],\n", "        save_period=config['save_period'],\n", "        workers=config['workers'],\n", "        optimizer=config['optimizer'],\n", "        lr0=config['lr0'],\n", "        momentum=config['momentum'],\n", "        weight_decay=config['weight_decay'],\n", "        amp=config['amp'],\n", "        exist_ok=True,\n", "    )\n", "    \n", "    val_results = model.val()\n", "    \n", "    all_results[f'fold{fold}'] = {\n", "        'mask_mAP50': float(val_results.seg.map50),\n", "        'mask_mAP50_95': float(val_results.seg.map),\n", "    }\n", "    \n", "    print(f\"Fold {fold}: mAP50={all_results[f'fold{fold}']['mask_mAP50']:.3f}, mAP50-95={all_results[f'fold{fold}']['mask_mAP50_95']:.3f}\")\n", "\n", "print(\"\\n\" + \"=\"*60 + \"\\nTraining Complete\\n\" + \"=\"*60)"]}, {"cell_type": "markdown", "id": "da7b1cb8", "metadata": {}, "source": ["## 5. Results Summary"]}, {"cell_type": "code", "execution_count": null, "id": "5bdfffb5", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "df = pd.DataFrame(all_results).T\n", "df['Fold'] = df.index\n", "df = df[['Fold', 'mask_mAP50', 'mask_mAP50_95']]\n", "\n", "mean_row = pd.DataFrame([{\n", "    'Fold': 'Mean',\n", "    'mask_mAP50': df['mask_mAP50'].mean(),\n", "    'mask_mAP50_95': df['mask_mAP50_95'].mean()\n", "}])\n", "std_row = pd.DataFrame([{\n", "    'Fold': 'Std',\n", "    'mask_mAP50': df['mask_mAP50'].std(),\n", "    'mask_mAP50_95': df['mask_mAP50_95'].std()\n", "}])\n", "\n", "df = pd.concat([df, mean_row, std_row], ignore_index=True)\n", "print(df.to_string(index=False))\n", "\n", "df.to_csv(results_dir / 'cross_validation_results.csv', index=False)\n", "print(f\"\\nSaved: {results_dir / 'cross_validation_results.csv'}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}