# FaciesNet Improvement Plan

**Date**: October 30, 2025  
**Task**: Instance segmentation of microfacies components in carbonate thin sections  
**Dataset**: 18 images, 5 classes, ~2674 instances after removing rare classes

---

## Current Results (Baseline)

| Model | Metric | Performance | Notes |
|-------|--------|-------------|-------|
| **YOLOv8-seg** | mAP50 | 38.96% | Best baseline, decent performance |
| **SAM (zero-shot)** | F1 / Precision / Recall | 63.7% / 52.6% / 81.4% | High recall, low precision (oversegments) |
| **Mask R-CNN** | AP / AP50 | 11.6% / 16.4% | Severely underperforming |

**Key Finding**: YOLOv8 performs best, Mask R-CNN unexpectedly weak

---

## Core Problems Identified

### 1. **Severe Class Imbalance**
| Class | Instances | Percentage |
|-------|-----------|------------|
| Ooid | ~1400 | 52% |
| Peloid | ~430 | 16% |
| Broken ooid | ~400 | 15% |
| Intraclast | ~350 | 13% |
| Ostracod | ~90 | 3.4% |

**Impact**: Models likely ignore minority classes (Ostracod)

### 2. **Small Dataset**
- Only 18 images for supervised learning
- 5-fold CV means only 14-15 training images per fold
- Deep learning typically needs 100s-1000s of images

### 3. **Domain Shift**
- Models pretrained on COCO/ImageNet (everyday objects)
- Thin-section microscopy has different texture/features
- Grain boundaries are fuzzy/ambiguous

### 4. **Not Addressed Yet**
- No class weighting applied
- No focal loss for imbalance
- No multi-scale optimization
- No test-time augmentation
- No per-class performance analysis

---

## Model Selection

### ✅ **Primary Approach: YOLOv8-seg (Improved)**

**Why chosen:**
- Already works (39% mAP50 baseline)
- Fast training (~3 hours per fold)
- Easy to add class weights
- Built-in augmentation and multi-scale
- Best for iteration speed

**Planned improvements:**
1. Add class weights to config
2. Increase epochs (50 → 100-150)
3. Try larger model (yolov8m-seg or yolov8l-seg)
4. Tune anchor boxes for object sizes
5. Add stronger augmentation (mosaic, mixup)
6. Multi-scale training optimization
7. Test-time augmentation (TTA)

### 🔄 **Secondary Approach: SAM + Balanced Classifier**

**Why worth trying:**
- SAM has 81% recall (finds most objects)
- Only need to fix precision (52% → 80%+)
- Can train classifier with heavy class balancing
- Two-stage pipeline is interpretable

**Implementation plan:**
1. Use SAM for mask proposals
2. Extract features from each mask (texture, shape, color)
3. Train balanced classifier (Random Forest or lightweight CNN)
4. Post-process to filter false positives

### 🔄 **Tertiary Approach: Cascade Mask R-CNN**

**When to use:**
- If YOLO plateaus <50% mAP50
- If pixel-perfect masks are required
- As final comparison benchmark

---

## Improvement Strategy

### Phase 1: YOLOv8-seg Optimization (START HERE)

**Step 1: Add Class Weights**
- Calculate inverse frequency weights
- Add to YOLO config file
- Retrain and measure per-class mAP

**Step 2: Hyperparameter Tuning**
- Increase epochs: 50 → 100 → 150
- Try larger model: yolov8s-seg → yolov8m-seg → yolov8l-seg
- Adjust learning rate if needed
- Tune confidence thresholds per class

**Step 3: Advanced Techniques**
- Enable/tune mosaic and mixup augmentation
- Test-time augmentation (flip, rotate, multi-scale)
- Hard example mining
- Ensemble multiple fold models

**Target**: mAP50 > 50-60%

### Phase 2: SAM + Classifier Hybrid

**Only if YOLOv8 plateaus or needs better precision**

**Step 1: SAM Proposal Generation**
- Use existing SAM implementation (already done)
- Tune SAM parameters for better precision/recall tradeoff
- Cache SAM proposals for all images

**Step 2: Feature Extraction**
- From each SAM mask, extract:
  - Texture features (LBP, Gabor)
  - Shape features (area, perimeter, circularity)
  - Color features (mean RGB, histogram)
  - Context features (surrounding texture)

**Step 3: Balanced Classifier**
- Train with class weights or SMOTE oversampling
- Try: Random Forest, XGBoost, or lightweight CNN
- Optimize for minority class performance

**Target**: F1 > 70%, Precision > 80%

### Phase 3: Mask R-CNN Fixes (If Needed)

**Only if required for comparison or publication**

**Fixes to try:**
- Add focal loss for classification
- Add class weights to all loss components
- Increase training iterations (3000 → 5000-10000)
- Lower learning rate (0.001 → 0.0005)
- Use Cascade Mask R-CNN instead
- Add test-time augmentation

---

## Evaluation Metrics

### Primary Metrics
- **mAP50** (main metric, IoU=0.5)
- **mAP** (COCO style, IoU=0.5:0.95)
- **Per-class AP50** (detect imbalance issues)

### Secondary Metrics
- **Precision** (avoid false positives)
- **Recall** (find all objects)
- **F1 Score** (balance precision/recall)
- **Confusion Matrix** (per-class errors)

### Analysis Needed
- [ ] Generate per-class AP50 scores
- [ ] Visualize predictions on validation images
- [ ] Analyze failure modes (FP, FN by class)
- [ ] Plot precision-recall curves per class

---

## Success Criteria

### Minimum Viable Performance
- **Overall mAP50**: > 50%
- **Minority class (Ostracod) AP50**: > 30%
- **Precision**: > 70% (avoid too many false positives)

### Good Performance
- **Overall mAP50**: > 60%
- **All classes AP50**: > 40%
- **Balanced performance** across classes (no class <20% AP50)

### Excellent Performance
- **Overall mAP50**: > 70%
- **All classes AP50**: > 50%
- **Usable for scientific analysis**

---

## Next Steps

### Immediate (When User Says Start)
1. ✅ Create this plan document
2. ⏳ Calculate class weights from dataset
3. ⏳ Update YOLOv8 config with class weights
4. ⏳ Add per-class evaluation to training notebook
5. ⏳ Retrain YOLOv8 with improvements

### Short-term
- Run improved YOLO training (all 5 folds)
- Analyze per-class results
- Visualize predictions (good/bad examples)
- Iterate based on failure modes

### Medium-term
- Try larger YOLO models if needed
- Implement SAM + classifier hybrid
- Compare all approaches
- Select best model

### Long-term
- Fine-tune best model
- Test-time augmentation
- Ensemble if beneficial
- Document final performance

---

## Implementation Checklist

### YOLOv8 Improvements
- [ ] Calculate inverse frequency class weights
- [ ] Update fold config YAMLs with class weights
- [ ] Increase epochs to 100-150
- [ ] Add per-class mAP evaluation
- [ ] Add prediction visualization
- [ ] Retrain all 5 folds
- [ ] Analyze results and iterate

### SAM Hybrid (If Needed)
- [ ] Optimize SAM parameters
- [ ] Implement feature extraction
- [ ] Train balanced classifier
- [ ] Evaluate hybrid pipeline

### Mask R-CNN Fixes (If Needed)
- [ ] Add focal loss
- [ ] Add class weights
- [ ] Increase iterations
- [ ] Compare with improved YOLO

---

## Notes

- **Priority**: Accuracy > Speed > Interpretability
- **Dataset limitation**: 18 images is very small for deep learning
- **Transfer learning is critical**: Pretrained models essential
- **Class imbalance is the main challenge**: Must address explicitly
- **Iteration speed matters**: YOLO allows faster experimentation than Mask R-CNN

---

**Status**: Ready to start YOLOv8 improvements (waiting for user signal)
