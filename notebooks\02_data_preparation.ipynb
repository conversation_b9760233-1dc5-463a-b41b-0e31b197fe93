{"cells": [{"cell_type": "markdown", "id": "6e6a008d", "metadata": {}, "source": ["# Data Preparation\n", "\n", "Convert LabelMe annotations to COCO and YOLO formats.\n", "Create 5-fold stratified cross-validation splits."]}, {"cell_type": "code", "execution_count": 1, "id": "9ff24ef3", "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from PIL import Image, ImageDraw\n", "from collections import Counter\n", "from sklearn.model_selection import StratifiedKFold\n", "from datetime import datetime\n", "import shutil\n", "\n", "# Paths\n", "DATA_DIR = Path('../data/raw')\n", "PROCESSED_DIR = Path('../data/processed')\n", "OUTPUT_DIR = Path('../outputs/visualizations')\n", "\n", "# Create directories\n", "for subdir in ['coco', 'yolo/images', 'yolo/labels', 'splits']:\n", "    (PROCESSED_DIR / subdir).mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "markdown", "id": "583d1612", "metadata": {}, "source": ["## 1. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 2, "id": "6d0ac321", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 18 images, 2674 annotations\n", "Excluded: Bivalve, Quartz grain (insufficient instances)\n"]}], "source": ["def load_labelme_data(data_dir):\n", "    json_files = sorted(data_dir.glob('*.json'))\n", "    dataset = []\n", "    \n", "    # Classes to exclude (too few instances)\n", "    exclude_classes = ['Bivalve', 'Quartz grain']\n", "    \n", "    for json_file in json_files:\n", "        with open(json_file) as f:\n", "            data = json.load(f)\n", "        \n", "        image_path = data_dir / data['imagePath']\n", "        if not image_path.exists():\n", "            continue\n", "        \n", "        # Filter out excluded classes\n", "        data['shapes'] = [s for s in data['shapes'] if s['label'] not in exclude_classes]\n", "        \n", "        dataset.append({\n", "            'image_path': str(image_path),\n", "            'image_name': data['imagePath'],\n", "            'image_width': data['imageWidth'],\n", "            'image_height': data['imageHeight'],\n", "            'annotations': data['shapes']\n", "        })\n", "    \n", "    return dataset\n", "\n", "dataset = load_labelme_data(DATA_DIR)\n", "print(f\"Loaded {len(dataset)} images, {sum(len(d['annotations']) for d in dataset)} annotations\")\n", "print(\"Excluded: Bivalve, Quartz grain (insufficient instances)\")\n"]}, {"cell_type": "markdown", "id": "8c4fd822", "metadata": {}, "source": ["## 2. Class Mapping"]}, {"cell_type": "code", "execution_count": 3, "id": "05f6b268", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0: <PERSON> ooid\n", "1: Intraclast\n", "2: <PERSON><PERSON>\n", "3: Ostracod\n", "4: <PERSON><PERSON><PERSON>\n"]}], "source": ["# Extract all unique classes\n", "all_classes = sorted(set(\n", "    ann['label'] for d in dataset for ann in d['annotations']\n", "))\n", "\n", "class_to_id = {name: idx for idx, name in enumerate(all_classes)}\n", "id_to_class = {idx: name for name, idx in class_to_id.items()}\n", "\n", "for name, idx in class_to_id.items():\n", "    print(f\"{idx}: {name}\")"]}, {"cell_type": "markdown", "id": "38c9104f", "metadata": {}, "source": ["## 3. Convert to COCO Format"]}, {"cell_type": "code", "execution_count": 4, "id": "ef57040c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COCO: 18 images, 2674 annotations\n"]}], "source": ["def polygon_to_bbox(points):\n", "    \"\"\"Convert polygon to [x, y, width, height] bounding box.\"\"\"\n", "    points = np.array(points)\n", "    x_min, y_min = points.min(axis=0)\n", "    x_max, y_max = points.max(axis=0)\n", "    return [float(x_min), float(y_min), float(x_max - x_min), float(y_max - y_min)]\n", "\n", "def polygon_area(points):\n", "    \"\"\"Calculate polygon area using <PERSON><PERSON><PERSON> formula.\"\"\"\n", "    points = np.array(points)\n", "    x, y = points[:, 0], points[:, 1]\n", "    return 0.5 * np.abs(np.dot(x, np.roll(y, 1)) - np.dot(y, np.roll(x, 1)))\n", "\n", "def labelme_to_coco(dataset, class_to_id):\n", "    coco = {\n", "        'images': [],\n", "        'annotations': [],\n", "        'categories': [{'id': idx, 'name': name} for name, idx in class_to_id.items()]\n", "    }\n", "    \n", "    annotation_id = 1\n", "    \n", "    for image_id, image_data in enumerate(dataset, start=1):\n", "        coco['images'].append({\n", "            'id': image_id,\n", "            'file_name': image_data['image_name'],\n", "            'width': image_data['image_width'],\n", "            'height': image_data['image_height']\n", "        })\n", "        \n", "        for ann in image_data['annotations']:\n", "            if ann['shape_type'] != 'polygon':\n", "                continue\n", "            \n", "            points = ann['points']\n", "            segmentation = [float(coord) for point in points for coord in point]\n", "            \n", "            coco['annotations'].append({\n", "                'id': annotation_id,\n", "                'image_id': image_id,\n", "                'category_id': class_to_id[ann['label']],\n", "                'segmentation': [segmentation],\n", "                'bbox': polygon_to_bbox(points),\n", "                'area': float(polygon_area(points)),\n", "                'iscrowd': 0\n", "            })\n", "            annotation_id += 1\n", "    \n", "    return coco\n", "\n", "coco_data = labelme_to_coco(dataset, class_to_id)\n", "print(f\"COCO: {len(coco_data['images'])} images, {len(coco_data['annotations'])} annotations\")"]}, {"cell_type": "markdown", "id": "91405d67", "metadata": {}, "source": ["## 4. <PERSON><PERSON> 5-<PERSON><PERSON> <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 5, "id": "6da0853b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fold 1: 14 train, 4 val\n", "Fold 2: 14 train, 4 val\n", "Fold 3: 14 train, 4 val\n", "Fold 4: 15 train, 3 val\n", "Fold 5: 15 train, 3 val\n"]}], "source": ["def get_dominant_class(image_data):\n", "    \"\"\"Get most common class in image (for stratification).\"\"\"\n", "    labels = [ann['label'] for ann in image_data['annotations']]\n", "    return Counter(labels).most_common(1)[0][0] if labels else 'Peloid'\n", "\n", "# Prepare stratification labels\n", "image_names = [d['image_name'] for d in dataset]\n", "dominant_classes = [get_dominant_class(d) for d in dataset]\n", "y_stratify = [class_to_id[cls] for cls in dominant_classes]\n", "\n", "# Create splits\n", "n_splits = 5\n", "skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)\n", "splits_info = []\n", "\n", "for fold, (train_idx, val_idx) in enumerate(skf.split(image_names, y_stratify), start=1):\n", "    split = {\n", "        'fold': fold,\n", "        'train': [image_names[i] for i in train_idx],\n", "        'val': [image_names[i] for i in val_idx]\n", "    }\n", "    splits_info.append(split)\n", "    print(f\"Fold {fold}: {len(split['train'])} train, {len(split['val'])} val\")"]}, {"cell_type": "markdown", "id": "3210c448", "metadata": {}, "source": ["## 5. Save COCO Splits"]}, {"cell_type": "code", "execution_count": 6, "id": "cc228fb0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved COCO files to ..\\data\\processed\\coco\n"]}], "source": ["def filter_coco_by_images(coco_data, image_names):\n", "    \"\"\"Create COCO subset for specific images.\"\"\"\n", "    image_ids = [\n", "        img['id'] for img in coco_data['images'] \n", "        if img['file_name'] in image_names\n", "    ]\n", "    \n", "    return {\n", "        'images': [img for img in coco_data['images'] if img['id'] in image_ids],\n", "        'annotations': [ann for ann in coco_data['annotations'] if ann['image_id'] in image_ids],\n", "        'categories': coco_data['categories']\n", "    }\n", "\n", "coco_dir = PROCESSED_DIR / 'coco'\n", "\n", "for split in splits_info:\n", "    fold = split['fold']\n", "    \n", "    # Save train and val\n", "    for split_name in ['train', 'val']:\n", "        fold_coco = filter_coco_by_images(coco_data, split[split_name])\n", "        output_path = coco_dir / f\"fold{fold}_{split_name}.json\"\n", "        with open(output_path, 'w') as f:\n", "            json.dump(fold_coco, f)\n", "\n", "# Save full dataset\n", "with open(coco_dir / 'full_dataset.json', 'w') as f:\n", "    json.dump(coco_data, f)\n", "\n", "print(f\"Saved COCO files to {coco_dir}\")"]}, {"cell_type": "markdown", "id": "4cb1dc37", "metadata": {}, "source": ["## 6. Convert to YOLO Format"]}, {"cell_type": "code", "execution_count": null, "id": "6e28a9f0", "metadata": {}, "outputs": [], "source": ["def create_yolo_annotation(annotation, img_width, img_height, class_to_id):\n", "    \"\"\"Convert annotation to YOLO format: <class_id> <norm_x1> <norm_y1> ...\"\"\"\n", "    if annotation['shape_type'] != 'polygon':\n", "        return None\n", "    \n", "    class_id = class_to_id[annotation['label']]\n", "    points = annotation['points']\n", "    \n", "    # Normalize coordinates to [0, 1]\n", "    normalized = [coord / dim for point in points for coord, dim in zip(point, [img_width, img_height])]\n", "    \n", "    return f\"{class_id} \" + \" \".join(f\"{c:.6f}\" for c in normalized)\n", "\n", "def save_yolo_split(dataset, image_names, fold_name, yolo_dir, class_to_id):\n", "    labels_dir = yolo_dir / 'labels' / fold_name\n", "    images_dir = yolo_dir / 'images' / fold_name\n", "    \n", "    labels_dir.mkdir(parents=True, exist_ok=True)\n", "    images_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    for img_data in dataset:\n", "        if img_data['image_name'] not in image_names:\n", "            continue\n", "        \n", "        # Create label file\n", "        stem = Path(img_data['image_name']).stem\n", "        lines = []\n", "        \n", "        for ann in img_data['annotations']:\n", "            line = create_yolo_annotation(\n", "                ann, img_data['image_width'], img_data['image_height'], class_to_id\n", "            )\n", "            if line:\n", "                lines.append(line)\n", "        \n", "        with open(labels_dir / f\"{stem}.txt\", 'w') as f:\n", "            f.write('\\n'.join(lines))\n", "        \n", "        # Copy image\n", "        shutil.copy2(img_data['image_path'], images_dir / img_data['image_name'])\n", "\n", "yolo_dir = PROCESSED_DIR / 'yolo'\n", "\n", "for split in splits_info:\n", "    fold = split['fold']\n", "    save_yolo_split(dataset, split['train'], f\"fold{fold}_train\", yolo_dir, class_to_id)\n", "    save_yolo_split(dataset, split['val'], f\"fold{fold}_val\", yolo_dir, class_to_id)\n", "\n", "print(f\"Saved YOLO files to {yolo_dir}\")"]}, {"cell_type": "markdown", "id": "3475ebe1", "metadata": {}, "source": ["## 7. <PERSON><PERSON> YOL<PERSON> Configs"]}, {"cell_type": "code", "execution_count": null, "id": "c3326131", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 5 YOLO config files\n"]}], "source": ["for split in splits_info:\n", "    fold = split['fold']\n", "    # Use relative path for Colab compatibility\n", "    config = f\"\"\"path: data/processed/yolo\n", "train: images/fold{fold}_train\n", "val: images/fold{fold}_val\n", "nc: {len(all_classes)}\n", "names: {all_classes}\n", "\"\"\"\n", "    \n", "    with open(yolo_dir / f\"fold{fold}_config.yaml\", 'w') as f:\n", "        f.write(config)\n", "\n", "print(f\"Created {n_splits} YOLO config files\")\n"]}, {"cell_type": "markdown", "id": "5e8e8db1", "metadata": {}, "source": ["## 8. Save Split Info"]}, {"cell_type": "code", "execution_count": 9, "id": "30a8bb5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved split info to ..\\data\\processed\\splits\\fold_splits.json\n"]}], "source": ["splits_file = PROCESSED_DIR / 'splits' / 'fold_splits.json'\n", "with open(splits_file, 'w') as f:\n", "    json.dump({\n", "        'n_folds': n_splits,\n", "        'random_state': 42,\n", "        'created': datetime.now().isoformat(),\n", "        'class_mapping': class_to_id,\n", "        'splits': splits_info\n", "    }, f, indent=2)\n", "\n", "print(f\"Saved split info to {splits_file}\")"]}, {"cell_type": "markdown", "id": "9ae88b62", "metadata": {}, "source": ["## 9. Validate Conversion"]}, {"cell_type": "code", "execution_count": null, "id": "9c3f8e0a", "metadata": {}, "outputs": [], "source": ["# Visualize one sample\n", "sample_image = splits_info[0]['val'][0]\n", "image_id = next(img['id'] for img in coco_data['images'] if img['file_name'] == sample_image)\n", "annotations = [ann for ann in coco_data['annotations'] if ann['image_id'] == image_id]\n", "\n", "img = Image.open(DATA_DIR / sample_image)\n", "draw = ImageDraw.Draw(img)\n", "\n", "colors = ['red', 'blue', 'green', 'yellow', 'cyan', 'magenta', 'orange']\n", "\n", "for ann in annotations:\n", "    points = ann['segmentation'][0]\n", "    points_tuples = [(points[i], points[i+1]) for i in range(0, len(points), 2)]\n", "    color = colors[ann['category_id'] % len(colors)]\n", "    draw.polygon(points_tuples, outline=color, width=2)\n", "\n", "plt.figure(figsize=(15, 12))\n", "plt.imshow(img)\n", "plt.title(f'Validation: {sample_image} ({len(annotations)} instances)')\n", "plt.axis('off')\n", "plt.savefig(OUTPUT_DIR / 'data_prep_validation.png', dpi=150, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "17b63873", "metadata": {}, "source": ["## Summary"]}, {"cell_type": "code", "execution_count": null, "id": "39af9630", "metadata": {}, "outputs": [], "source": ["print(f\"\"\"\n", "Data Preparation Complete\n", "{'='*50}\n", "Images: {len(dataset)}\n", "Annotations: {len(coco_data['annotations'])}\n", "Classes: {len(all_classes)}\n", "Folds: {n_splits}\n", "\n", "Created:\n", "  COCO: {coco_dir}\n", "  YOLO: {yolo_dir}\n", "  Splits: {splits_file}\n", "\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}