{"n_folds": 5, "random_state": 42, "created": "2025-10-29T15:57:19.815342", "class_mapping": {"Broken ooid": 0, "Intraclast": 1, "Ooid": 2, "Ostracod": 3, "Peloid": 4}, "splits": [{"fold": 1, "train": ["MB12_ES0081.jpg", "WL5_IM10041.jpg", "WLC8_ES0023.JPG", "WLC8_ES0025.JPG", "WLC8_ES0026.JPG", "WT10-ES0004.jpg", "WT10_ES0039.JPG", "WT12-ES0021.jpg", "WT13-ES0023.jpg", "WT13-ES0026.jpg", "WT13_ES0045.JPG", "WT13_ES0049.JPG", "WT13_ES0056.JPG", "WT15-ES0042.jpg"], "val": ["GN10-ES0086.jpg", "HB4_ES0068.jpg", "WLC8_ES0022.JPG", "WT10-ES0044.JPG"]}, {"fold": 2, "train": ["GN10-ES0086.jpg", "HB4_ES0068.jpg", "MB12_ES0081.jpg", "WLC8_ES0022.JPG", "WLC8_ES0023.JPG", "WLC8_ES0025.JPG", "WLC8_ES0026.JPG", "WT10-ES0004.jpg", "WT10-ES0044.JPG", "WT10_ES0039.JPG", "WT12-ES0021.jpg", "WT13-ES0026.jpg", "WT13_ES0049.JPG", "WT13_ES0056.JPG"], "val": ["WL5_IM10041.jpg", "WT13-ES0023.jpg", "WT13_ES0045.JPG", "WT15-ES0042.jpg"]}, {"fold": 3, "train": ["GN10-ES0086.jpg", "HB4_ES0068.jpg", "WL5_IM10041.jpg", "WLC8_ES0022.JPG", "WLC8_ES0023.JPG", "WLC8_ES0025.JPG", "WLC8_ES0026.JPG", "WT10-ES0044.JPG", "WT12-ES0021.jpg", "WT13-ES0023.jpg", "WT13-ES0026.jpg", "WT13_ES0045.JPG", "WT13_ES0056.JPG", "WT15-ES0042.jpg"], "val": ["MB12_ES0081.jpg", "WT10-ES0004.jpg", "WT10_ES0039.JPG", "WT13_ES0049.JPG"]}, {"fold": 4, "train": ["GN10-ES0086.jpg", "HB4_ES0068.jpg", "MB12_ES0081.jpg", "WL5_IM10041.jpg", "WLC8_ES0022.JPG", "WLC8_ES0025.JPG", "WLC8_ES0026.JPG", "WT10-ES0004.jpg", "WT10-ES0044.JPG", "WT10_ES0039.JPG", "WT12-ES0021.jpg", "WT13-ES0023.jpg", "WT13_ES0045.JPG", "WT13_ES0049.JPG", "WT15-ES0042.jpg"], "val": ["WLC8_ES0023.JPG", "WT13-ES0026.jpg", "WT13_ES0056.JPG"]}, {"fold": 5, "train": ["GN10-ES0086.jpg", "HB4_ES0068.jpg", "MB12_ES0081.jpg", "WL5_IM10041.jpg", "WLC8_ES0022.JPG", "WLC8_ES0023.JPG", "WT10-ES0004.jpg", "WT10-ES0044.JPG", "WT10_ES0039.JPG", "WT13-ES0023.jpg", "WT13-ES0026.jpg", "WT13_ES0045.JPG", "WT13_ES0049.JPG", "WT13_ES0056.JPG", "WT15-ES0042.jpg"], "val": ["WLC8_ES0025.JPG", "WLC8_ES0026.JPG", "WT12-ES0021.jpg"]}]}